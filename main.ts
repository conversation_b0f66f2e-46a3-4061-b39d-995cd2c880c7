import { Hono } from 'hono'
import { z } from "npm:zod@3.22.4"

const app = new Hono()

app.get('/', (c) => {
  return c.text('Hello Hono!')
})

app.get("/entry/:id", (c) => {
  const id = c.req.param('id')
  return c.json({
    "your id is ": id
  })
})

app.get('/hello', (c) => {
  const schema = z.object({ name: z.string() })
  const params = c.req.query()
  const result = schema.safeParse(params)
  if (!result.success) {
    return c.json({ error: 'Invalid query' }, 400)
  }
  return c.json({
    message: `Hello! ${result.data.name}`
  })
})

app.use
app.use(async (c, next) => {
  const start = Date.now()
  await next()
  const end = Date.now()
  c.res.headers.set('X-Response-Time', `${end - start}ms`)
})
Deno.serve({port: 80},app.fetch)
